"""
Demo chạy song song nhiều retriever với Neo4j GraphRAG
Sử dụng concurrent futures để tìm kiếm bảng và cột dựa trên câu hỏi
"""

import concurrent.futures
import time
from typing import List, Dict, Any
from utils import get_driver, get_embedding
import numpy as np


class MockRetriever:
    """Mock retriever class để demo - thay thế bằng retriever thật"""
    
    def __init__(self, name: str, retriever_type: str):
        self.name = name
        self.retriever_type = retriever_type
        
    def retrieve(self, query: str, top_k: int = 3) -> List[Dict[str, Any]]:
        """
        Mock retrieve function - trong thực tế sẽ gọi retriever.search()
        Trả về list dict {name, score}
        """
        # Simulate processing time
        time.sleep(0.1)
        
        # Mock data based on retriever type
        if self.retriever_type == "table":
            mock_results = [
                {"name": f"SINHVIEN_{self.name[-1]}", "score": 0.95},
                {"name": f"DIEM_{self.name[-1]}", "score": 0.88},
                {"name": f"LOP_{self.name[-1]}", "score": 0.82},
                {"name": f"MONHOC_{self.name[-1]}", "score": 0.75},
                {"name": f"KHOA_{self.name[-1]}", "score": 0.68}
            ]
        else:  # column type
            prefix = "DM_" if "dm" in self.name.lower() else "DT_"
            mock_results = [
                {"name": f"{prefix}GPA", "score": 0.92},
                {"name": f"{prefix}DIEM_TB", "score": 0.89},
                {"name": f"{prefix}LOP", "score": 0.85},
                {"name": f"{prefix}MALOP", "score": 0.78},
                {"name": f"{prefix}TENSV", "score": 0.72}
            ]
        
        # Return top_k results
        return mock_results[:top_k]


class RealRetriever:
    """Real retriever wrapper cho Neo4j với vector similarity search"""

    def __init__(self, name: str, index_name: str, node_label: str,
                 text_property: str, extra_conditions: str = ""):
        self.name = name
        self.index_name = index_name
        self.node_label = node_label
        self.text_property = text_property
        self.extra_conditions = extra_conditions
        self.driver = get_driver()

    def retrieve(self, query: str, top_k: int = 3) -> List[Dict[str, Any]]:
        """
        Real retrieve function using Neo4j vector similarity search
        """
        try:
            # Get query embedding
            query_embedding = get_embedding(query)

            # Build Cypher query for vector similarity search
            cypher_query = f"""
            CALL db.index.vector.queryNodes('{self.index_name}', {top_k}, $queryEmbedding)
            YIELD node, score
            WHERE node:{self.node_label}
            {f"AND {self.extra_conditions}" if self.extra_conditions else ""}
            RETURN node.name as name, score
            ORDER BY score DESC
            LIMIT {top_k}
            """

            with self.driver.session() as session:
                result = session.run(cypher_query, queryEmbedding=query_embedding)

                formatted_results = []
                for record in result:
                    formatted_results.append({
                        "name": record["name"],
                        "score": float(record["score"])
                    })

                return formatted_results

        except Exception as e:
            print(f"Error in {self.name}: {e}")
            return []


class ParallelRetrieverSystem:
    """Hệ thống chạy song song nhiều retriever"""
    
    def __init__(self, use_real_retrievers: bool = False):
        self.use_real_retrievers = use_real_retrievers
        self.retrievers = self._initialize_retrievers()
    
    def _initialize_retrievers(self) -> Dict[str, Any]:
        """Khởi tạo các retriever"""
        if self.use_real_retrievers:
            return {
                "table_db1": RealRetriever(
                    name="table_db1",
                    index_name="table_db1_index",
                    node_label="Table",
                    text_property="description",
                    extra_conditions="n.database = 'EDU_HUCE'"
                ),
                "table_db2": RealRetriever(
                    name="table_db2", 
                    index_name="table_db2_index",
                    node_label="Table",
                    text_property="description",
                    extra_conditions="n.database = 'EDU_HUCE_DATA'"
                ),
                "column_dm": RealRetriever(
                    name="column_dm",
                    index_name="column_dm_index",
                    node_label="Column",
                    text_property="description",
                    extra_conditions="n.name STARTS WITH 'DM_'"
                ),
                "column_dt": RealRetriever(
                    name="column_dt",
                    index_name="column_dt_index", 
                    node_label="Column",
                    text_property="description",
                    extra_conditions="n.name STARTS WITH 'DT_'"
                )
            }
        else:
            return {
                "table_db1": MockRetriever("table_db1", "table"),
                "table_db2": MockRetriever("table_db2", "table"),
                "column_dm": MockRetriever("column_dm", "column"),
                "column_dt": MockRetriever("column_dt", "column")
            }
    
    def retrieve_single(self, retriever_name: str, query: str, top_k: int = 3) -> Dict[str, Any]:
        """Chạy một retriever đơn lẻ"""
        start_time = time.time()
        
        try:
            retriever = self.retrievers[retriever_name]
            results = retriever.retrieve(query, top_k)
            
            end_time = time.time()
            
            return {
                "retriever": retriever_name,
                "results": results,
                "execution_time": end_time - start_time,
                "status": "success"
            }
        except Exception as e:
            end_time = time.time()
            return {
                "retriever": retriever_name,
                "results": [],
                "execution_time": end_time - start_time,
                "status": "error",
                "error": str(e)
            }
    
    def retrieve_parallel(self, query: str, top_k: int = 3) -> Dict[str, Any]:
        """Chạy song song tất cả retrievers"""
        start_time = time.time()
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=4) as executor:
            # Submit all retriever tasks
            future_to_retriever = {
                executor.submit(self.retrieve_single, name, query, top_k): name
                for name in self.retrievers.keys()
            }
            
            results = {}
            
            # Collect results as they complete
            for future in concurrent.futures.as_completed(future_to_retriever):
                retriever_name = future_to_retriever[future]
                try:
                    result = future.result()
                    results[retriever_name] = result
                except Exception as e:
                    results[retriever_name] = {
                        "retriever": retriever_name,
                        "results": [],
                        "execution_time": 0,
                        "status": "error",
                        "error": str(e)
                    }
        
        end_time = time.time()
        
        return {
            "query": query,
            "total_execution_time": end_time - start_time,
            "retriever_results": results,
            "combined_results": self._combine_results(results)
        }
    
    def _combine_results(self, retriever_results: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Kết hợp kết quả từ tất cả retrievers theo điểm similarity"""
        all_results = []
        
        for retriever_name, result_data in retriever_results.items():
            if result_data["status"] == "success":
                for item in result_data["results"]:
                    all_results.append({
                        "name": item["name"],
                        "score": item["score"],
                        "retriever": retriever_name
                    })
        
        # Sort by score descending
        all_results.sort(key=lambda x: x["score"], reverse=True)
        
        return all_results
    
    def print_results(self, results: Dict[str, Any]):
        """In kết quả một cách đẹp mắt"""
        print(f"\n{'='*60}")
        print(f"QUERY: {results['query']}")
        print(f"TOTAL EXECUTION TIME: {results['total_execution_time']:.3f}s")
        print(f"{'='*60}")
        
        # Print individual retriever results
        for retriever_name, result_data in results["retriever_results"].items():
            print(f"\n📊 {retriever_name.upper()} (Time: {result_data['execution_time']:.3f}s)")
            print("-" * 40)
            
            if result_data["status"] == "success":
                for i, item in enumerate(result_data["results"], 1):
                    print(f"  {i}. {item['name']} (Score: {item['score']:.3f})")
            else:
                print(f"  ❌ Error: {result_data.get('error', 'Unknown error')}")
        
        # Print combined top results
        print(f"\n🏆 TOP COMBINED RESULTS")
        print("-" * 40)
        for i, item in enumerate(results["combined_results"][:10], 1):
            print(f"  {i}. {item['name']} (Score: {item['score']:.3f}) [{item['retriever']}]")


def main():
    """Hàm main demo"""
    print("🚀 Demo Parallel Retriever System với Neo4j GraphRAG")
    
    # Khởi tạo hệ thống (sử dụng mock retrievers để demo)
    system = ParallelRetrieverSystem(use_real_retrievers=False)
    
    # Câu hỏi demo
    test_queries = [
        "GPA trung bình của lớp 65IT3 là bao nhiêu",
        "Danh sách sinh viên khoa CNTT",
        "Điểm thi môn Toán cao đẳng"
    ]
    
    for query in test_queries:
        print(f"\n{'🔍 PROCESSING QUERY':<30}")
        results = system.retrieve_parallel(query, top_k=3)
        system.print_results(results)
        
        # Pause between queries
        time.sleep(1)
    
    print(f"\n{'✅ DEMO COMPLETED':<30}")


if __name__ == "__main__":
    main()
