import concurrent.futures
from retrievers import retriever_map  # Tập hợp retriever từ các file
from utils import get_embedding       # Hàm gọi model nhúng
import sys

query = sys.argv[1] if len(sys.argv) > 1 else "GPA trung bình của lớp 65IT3 là bao nhiêu"

def run_retriever(name, retriever):
    results = retriever.retrieve(query)
    return name, results

def main():
    print(f"\n🧠 Đang truy vấn: {query}\n")

    with concurrent.futures.ThreadPoolExecutor() as executor:
        futures = [executor.submit(run_retriever, name, retriever) for name, retriever in retriever_map.items()]
        results = [f.result() for f in futures]

    for name, res in results:
        print(f"\n📂 Retriever: {name}")
        for i, item in enumerate(res[:5]):
            print(f"  {i+1}. {item['name']} (score: {item['score']:.4f})")

if __name__ == "__main__":
    main()
