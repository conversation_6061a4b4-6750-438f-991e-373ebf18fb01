"""
<PERSON><PERSON><PERSON> hình cho các retrievers trong hệ thống
"""

# <PERSON>ấu hình Neo4j indexes và retrievers
RETRIEVER_CONFIGS = {
    "table_db1": {
        "index_name": "table_db1_index",
        "node_label": "Table", 
        "text_property": "description",
        "extra_conditions": "n.database = 'EDU_HUCE'",
        "description": "Bảng trong database EDU_HUCE"
    },
    "table_db2": {
        "index_name": "table_db2_index",
        "node_label": "Table",
        "text_property": "description", 
        "extra_conditions": "n.database = 'EDU_HUCE_DATA'",
        "description": "Bảng trong database EDU_HUCE_DATA"
    },
    "column_dm": {
        "index_name": "column_dm_index",
        "node_label": "Column",
        "text_property": "description",
        "extra_conditions": "n.name STARTS WITH 'DM_'",
        "description": "<PERSON>ột có tiền tố DM_ (<PERSON><PERSON>)"
    },
    "column_dt": {
        "index_name": "column_dt_index", 
        "node_label": "Column",
        "text_property": "description",
        "extra_conditions": "n.name STARTS WITH 'DT_'",
        "description": "Cột có tiền tố DT_ (Dữ liệu)"
    }
}

# Mock data cho demo
MOCK_DATA = {
    "table_db1": [
        {"name": "SINHVIEN", "score": 0.95},
        {"name": "DIEM", "score": 0.88}, 
        {"name": "LOP", "score": 0.82},
        {"name": "MONHOC", "score": 0.75},
        {"name": "KHOA", "score": 0.68}
    ],
    "table_db2": [
        {"name": "SINHVIEN_DATA", "score": 0.93},
        {"name": "DIEM_DATA", "score": 0.86},
        {"name": "LOP_DATA", "score": 0.80}, 
        {"name": "MONHOC_DATA", "score": 0.73},
        {"name": "KHOA_DATA", "score": 0.66}
    ],
    "column_dm": [
        {"name": "DM_GPA", "score": 0.92},
        {"name": "DM_DIEM_TB", "score": 0.89},
        {"name": "DM_LOP", "score": 0.85},
        {"name": "DM_MALOP", "score": 0.78},
        {"name": "DM_TENSV", "score": 0.72}
    ],
    "column_dt": [
        {"name": "DT_GPA", "score": 0.90},
        {"name": "DT_DIEM_TB", "score": 0.87},
        {"name": "DT_LOP", "score": 0.83},
        {"name": "DT_MALOP", "score": 0.76}, 
        {"name": "DT_TENSV", "score": 0.70}
    ]
}
