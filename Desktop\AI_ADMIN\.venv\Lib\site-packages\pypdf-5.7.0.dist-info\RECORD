pypdf-5.7.0.dist-info/INSTALLER,sha256=5hhM4Q4mYTT9z6QB6PGpUAW81PGNFrYrdXMj4oM_6ak,2
pypdf-5.7.0.dist-info/METADATA,sha256=amy0Vs-BtOEDPOSgwU71tB4me2abt_zFc5urfE1zAlo,7166
pypdf-5.7.0.dist-info/RECORD,,
pypdf-5.7.0.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
pypdf-5.7.0.dist-info/WHEEL,sha256=G2gURzTEtmeR8nrdXUJfNiB3VYVxigPQ-bEQujpNiNs,82
pypdf-5.7.0.dist-info/licenses/LICENSE,sha256=qXrCMOXzPvEKU2eoUOsB-R8aCwZONHQsd5TSKUVX9SQ,1605
pypdf/__init__.py,sha256=WYkiisiLw4TrsrobuzUkEFGwAUbPF8V8ei_HJSdEJNY,1302
pypdf/_cmap.py,sha256=A1L4NsHhm3LugMUt2ZJlQOuQP1U3-6jzZ8KrFf4ffMQ,18722
pypdf/_codecs/__init__.py,sha256=WXMkzlMCDlmG5U6ixQk8MrYxaQeJxEfig5DTaGlklLk,1676
pypdf/_codecs/_codecs.py,sha256=qRZMrUkTXUTtc5CxRYTuak-LmXXgk-yLpl1GFqhDx3A,10120
pypdf/_codecs/adobe_glyphs.py,sha256=t3cDFPDqwIz1w9B0gdVzjdc8eEK9AuRjk5f7laEw_fY,447213
pypdf/_codecs/pdfdoc.py,sha256=xfSvMFYsvxuaSQ0Uu9vZDKaB0Wu85h1uCiB1i9rAcUU,4269
pypdf/_codecs/std.py,sha256=DyQMuEpAGEpS9uy1jWf4cnj-kqShPOAij5sI7Q1YD8E,2630
pypdf/_codecs/symbol.py,sha256=nIaGQIlhWCJiPMHrwUlmGHH-_fOXyEKvguRmuKXcGAk,3734
pypdf/_codecs/zapfding.py,sha256=PQxjxRC616d41xF3exVxP1W8nM4QrZfjO3lmtLxpE_s,3742
pypdf/_crypt_providers/__init__.py,sha256=K3Z6AuXhXVeXgLet-Tukq2gt9H66OgdupsvxIS1CmkI,3054
pypdf/_crypt_providers/_base.py,sha256=_f53Mj6vivhEZMQ4vNxN5G0IOgFY-n5_leke0c_qiNU,1711
pypdf/_crypt_providers/_cryptography.py,sha256=zT3WmbPzesvgHRkGcKAldqJ24MY3BwZViVbSc55Zxhw,4557
pypdf/_crypt_providers/_fallback.py,sha256=vsYoowR1YCAV_q-HrdIZhkUcrCb6HvRBNMYm03QtCU8,3334
pypdf/_crypt_providers/_pycryptodome.py,sha256=U1aQZ9iYBrZo-hKCjJUhGOPhwEFToiitowQ316TNrrA,3381
pypdf/_doc_common.py,sha256=CkQsBSjkyFjzRoDnbOy907KwBmqzWPdxA38ko0nkxs8,52246
pypdf/_encryption.py,sha256=pPg7fIfqdL96Tc6RVoBytEVjMrmZFecr_6l7dbtDFrE,48775
pypdf/_merger.py,sha256=YfSQKDiiQz2WtCmVZjxP_nv2pR2shiBf2tDiAb41c7s,1744
pypdf/_page.py,sha256=vEF65ZOjqdoDxBEaDQHz6M6CnCz9aRzGP1yHOw_hyVk,99559
pypdf/_page_labels.py,sha256=cAPZXzXQ24LpoahlTNxVtuj8Rr4AczOKc0SlyVVXjmY,8532
pypdf/_protocols.py,sha256=noE1y2fVE-z1wq-FkQzaS5exa8ovOFTUXqdQSvqi57c,2142
pypdf/_reader.py,sha256=PWcWMy7zrMMYm3cIJRKuN1DnoTCG78dZTUhuyix9TYg,51552
pypdf/_text_extraction/__init__.py,sha256=7b2O18fKdcyhcWfziYBsHp-HgznyGJaHGbEcd88q580,8538
pypdf/_text_extraction/_layout_mode/__init__.py,sha256=k1tN46gDX1zhAatD8oTGMuCJUp-pgbHjyQ8H6axXRgU,338
pypdf/_text_extraction/_layout_mode/_fixed_width_page.py,sha256=uYdcnLHNVVSETiDdTgAz_2r9mZQT4rXAcHNlNQ7DT1Q,14915
pypdf/_text_extraction/_layout_mode/_font.py,sha256=Z334fpftHVERut1c3rjWvB49BdcaA_hFq3ZuWL5wcm0,7047
pypdf/_text_extraction/_layout_mode/_font_widths.py,sha256=Hfgsd2ftGw8Ajl7IcwNIlfLYnum-ekaadfwErcUdWtI,4265
pypdf/_text_extraction/_layout_mode/_text_state_manager.py,sha256=nH548Ri6A2jwIE0pwviWHSfadX9xNl-mfOQ539QARkI,8196
pypdf/_text_extraction/_layout_mode/_text_state_params.py,sha256=b8DSoJ2easCZW_JvMl84WFFIANKGhLD1zjMVAlqScyU,5318
pypdf/_utils.py,sha256=IF_gd0QuQdO6SOzNveF1AwI1HA_iKv6xy1F66n2dWmw,19473
pypdf/_version.py,sha256=QmHMXVnw5DVPfWzvN7FS1tOhDAesdxpM_aVOh9CMuSk,22
pypdf/_writer.py,sha256=lxmql_pEVQjBjzt_T2BPkWIkcUjcgZKEoM1z1vVt0ek,132769
pypdf/_xobj_image_helpers.py,sha256=G6BfIsJaw7RvAcWUdNY2oTtWKVEUKGiCqnC2f531BaA,13896
pypdf/annotations/__init__.py,sha256=f2k_-jAn39CCB27KxQ_e93GinnzkAHbUnnSeGJl1jyE,990
pypdf/annotations/_base.py,sha256=eeoc9v2w15jAUhKXj48l1bB66YgBgV-2v5IIUJH-vws,961
pypdf/annotations/_markup_annotations.py,sha256=akoAcQUA945XGgjXRnX6hX1xczTUeqrvciK7KiNArt4,10100
pypdf/annotations/_non_markup_annotations.py,sha256=yAIJTFE_8dDdel8sUKlOBTccf5onO8Foa8uluY50u2Y,3656
pypdf/constants.py,sha256=LktzwLRbe7lC9k7PfmJ2WFGv-4TnlC4Tffom0HIvyiw,22393
pypdf/errors.py,sha256=x0J5mTIbp5YcXA1pdYa5DO83uAhXP5NCO0Ankf4DsUY,1740
pypdf/filters.py,sha256=ILIl3-wKn4NwX19BeZwiNf-QiLS9MEgP7IG7k5b0O1o,33339
pypdf/generic/__init__.py,sha256=0qEtU1i03kvRUcJkD_aMz3dxaRljQEDH8JqPF_FoDzw,7202
pypdf/generic/_base.py,sha256=evLCcg8HaF2Pb25qhjCYT1tmdG_r2fOVy3eYR4qjxqQ,31179
pypdf/generic/_data_structures.py,sha256=K9YxvtnNg-3ZfKQ_9qzMKhxgPPHSP_ZD70_pMzihpqg,63695
pypdf/generic/_files.py,sha256=UcyL_mCDpVh_dRuxxH8bENWA76rYt5eFw0emFcOE79Y,5655
pypdf/generic/_fit.py,sha256=_pr3j4-awTjGOdhFe5GRno35BhsrX2oveLkr75VME-s,5528
pypdf/generic/_image_inline.py,sha256=B9jqOt_navgmjekp7BqNpu72SMccdHjNCo4_Jtez15w,11478
pypdf/generic/_outline.py,sha256=qKbMX42OWfqnopIiE6BUy6EvdTLGe3ZtjaiWN85JpaY,1094
pypdf/generic/_rectangle.py,sha256=5KJRbQESqdzrYvJOFcwfp0_v_bhCDVj9r4yMyGXSGyc,3808
pypdf/generic/_utils.py,sha256=p_5ZERuQggQGNrUVYHf3OxNn_tkIb0tYOhBo095_N5I,7277
pypdf/generic/_viewerpref.py,sha256=8grg_CQQPsV5BHnxjwTYyeIWAsu0v5KDWgRFEIDe_VQ,6768
pypdf/pagerange.py,sha256=T260YnndO6eZPrVrN4kp8NFQrYDzjOuM1CJG55bvcOg,7121
pypdf/papersizes.py,sha256=6Tz5sfNN_3JOUapY83U-lakohnpXYA0hSEQNmOVLFL8,1413
pypdf/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
pypdf/types.py,sha256=kc6IlQssx2j99po2ShSAWERhJXOVeh5IBw3kbp5oixU,1921
pypdf/xmp.py,sha256=m3B0IddnjHLgftYIiFw991gQbKG7Vud3oqHUT3rycOs,15758
