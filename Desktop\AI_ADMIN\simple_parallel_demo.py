"""
Demo đơn giản chạy song song nhiều retriever v<PERSON><PERSON> Neo4j
<PERSON>ử dụng concurrent futures để tìm kiếm bảng và cột dựa trên câu hỏi
"""

import concurrent.futures
import time
import random
from typing import List, Dict, Any


class SimpleRetriever:
    """Simple retriever class để demo chạy song song"""
    
    def __init__(self, name: str, retriever_type: str):
        self.name = name
        self.retriever_type = retriever_type
        
        # Mock data cho từng loại retriever
        if retriever_type == "table_db1":
            self.mock_data = [
                {"name": "SINHVIEN", "score": 0.95},
                {"name": "DIEM", "score": 0.88},
                {"name": "LOP", "score": 0.82},
                {"name": "MONHOC", "score": 0.75},
                {"name": "KHOA", "score": 0.68}
            ]
        elif retriever_type == "table_db2":
            self.mock_data = [
                {"name": "SINHVIEN_DATA", "score": 0.93},
                {"name": "DIEM_DATA", "score": 0.86},
                {"name": "LOP_DATA", "score": 0.80},
                {"name": "MONHOC_DATA", "score": 0.73},
                {"name": "KHOA_DATA", "score": 0.66}
            ]
        elif retriever_type == "column_dm":
            self.mock_data = [
                {"name": "DM_GPA", "score": 0.92},
                {"name": "DM_DIEM_TB", "score": 0.89},
                {"name": "DM_LOP", "score": 0.85},
                {"name": "DM_MALOP", "score": 0.78},
                {"name": "DM_TENSV", "score": 0.72}
            ]
        else:  # column_dt
            self.mock_data = [
                {"name": "DT_GPA", "score": 0.90},
                {"name": "DT_DIEM_TB", "score": 0.87},
                {"name": "DT_LOP", "score": 0.83},
                {"name": "DT_MALOP", "score": 0.76},
                {"name": "DT_TENSV", "score": 0.70}
            ]
        
    def retrieve(self, query: str, top_k: int = 3) -> List[Dict[str, Any]]:
        """
        Mock retrieve function - simulate processing time and return results
        """
        # Simulate processing time (0.1-0.5 seconds)
        processing_time = random.uniform(0.1, 0.5)
        time.sleep(processing_time)
        
        # Add some randomness to scores based on query relevance
        results = []
        for item in self.mock_data[:top_k]:
            # Simulate query relevance by adjusting scores slightly
            score_adjustment = random.uniform(-0.05, 0.05)
            adjusted_score = max(0.0, min(1.0, item["score"] + score_adjustment))
            
            results.append({
                "name": item["name"],
                "score": round(adjusted_score, 3)
            })
        
        return results


class ParallelRetrieverSystem:
    """Hệ thống chạy song song nhiều retriever"""
    
    def __init__(self):
        self.retrievers = {
            "table_db1": SimpleRetriever("table_db1", "table_db1"),
            "table_db2": SimpleRetriever("table_db2", "table_db2"),
            "column_dm": SimpleRetriever("column_dm", "column_dm"),
            "column_dt": SimpleRetriever("column_dt", "column_dt")
        }
    
    def retrieve_single(self, retriever_name: str, query: str, top_k: int = 3) -> Dict[str, Any]:
        """Chạy một retriever đơn lẻ"""
        start_time = time.time()
        
        try:
            retriever = self.retrievers[retriever_name]
            results = retriever.retrieve(query, top_k)
            
            end_time = time.time()
            
            return {
                "retriever": retriever_name,
                "results": results,
                "execution_time": round(end_time - start_time, 3),
                "status": "success"
            }
        except Exception as e:
            end_time = time.time()
            return {
                "retriever": retriever_name,
                "results": [],
                "execution_time": round(end_time - start_time, 3),
                "status": "error",
                "error": str(e)
            }
    
    def retrieve_parallel(self, query: str, top_k: int = 3) -> Dict[str, Any]:
        """Chạy song song tất cả retrievers"""
        start_time = time.time()
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=4) as executor:
            # Submit all retriever tasks
            future_to_retriever = {
                executor.submit(self.retrieve_single, name, query, top_k): name
                for name in self.retrievers.keys()
            }
            
            results = {}
            
            # Collect results as they complete
            for future in concurrent.futures.as_completed(future_to_retriever):
                retriever_name = future_to_retriever[future]
                try:
                    result = future.result()
                    results[retriever_name] = result
                except Exception as e:
                    results[retriever_name] = {
                        "retriever": retriever_name,
                        "results": [],
                        "execution_time": 0,
                        "status": "error",
                        "error": str(e)
                    }
        
        end_time = time.time()
        
        return {
            "query": query,
            "total_execution_time": round(end_time - start_time, 3),
            "retriever_results": results,
            "combined_results": self._combine_results(results)
        }
    
    def _combine_results(self, retriever_results: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Kết hợp kết quả từ tất cả retrievers theo điểm similarity"""
        all_results = []
        
        for retriever_name, result_data in retriever_results.items():
            if result_data["status"] == "success":
                for item in result_data["results"]:
                    all_results.append({
                        "name": item["name"],
                        "score": item["score"],
                        "retriever": retriever_name
                    })
        
        # Sort by score descending
        all_results.sort(key=lambda x: x["score"], reverse=True)
        
        return all_results
    
    def print_results(self, results: Dict[str, Any]):
        """In kết quả một cách đẹp mắt"""
        print(f"\n{'='*70}")
        print(f"🔍 QUERY: {results['query']}")
        print(f"⏱️  TOTAL EXECUTION TIME: {results['total_execution_time']}s")
        print(f"{'='*70}")
        
        # Print individual retriever results
        for retriever_name, result_data in results["retriever_results"].items():
            print(f"\n📊 {retriever_name.upper()} (Time: {result_data['execution_time']}s)")
            print("-" * 50)
            
            if result_data["status"] == "success":
                for i, item in enumerate(result_data["results"], 1):
                    print(f"  {i}. {item['name']:<20} (Score: {item['score']:.3f})")
            else:
                print(f"  ❌ Error: {result_data.get('error', 'Unknown error')}")
        
        # Print combined top results
        print(f"\n🏆 TOP COMBINED RESULTS")
        print("-" * 50)
        for i, item in enumerate(results["combined_results"][:10], 1):
            print(f"  {i}. {item['name']:<20} (Score: {item['score']:.3f}) [{item['retriever']}]")


def main():
    """Hàm main demo"""
    print("🚀 Demo Parallel Retriever System với Neo4j GraphRAG")
    print("📝 Sử dụng concurrent futures để chạy song song nhiều retriever")
    
    # Khởi tạo hệ thống
    system = ParallelRetrieverSystem()
    
    # Câu hỏi demo
    test_queries = [
        "GPA trung bình của lớp 65IT3 là bao nhiêu",
        "Danh sách sinh viên khoa CNTT", 
        "Điểm thi môn Toán cao đẳng"
    ]
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n{'🔍 PROCESSING QUERY ' + str(i):<30}")
        results = system.retrieve_parallel(query, top_k=3)
        system.print_results(results)
        
        # Pause between queries
        if i < len(test_queries):
            print("\n" + "⏳ Waiting 2 seconds before next query...")
            time.sleep(2)
    
    print(f"\n{'✅ DEMO COMPLETED':<30}")
    print("💡 Lưu ý: Đây là demo với mock data. Trong thực tế, thay thế bằng Neo4j vector search.")


if __name__ == "__main__":
    main()
